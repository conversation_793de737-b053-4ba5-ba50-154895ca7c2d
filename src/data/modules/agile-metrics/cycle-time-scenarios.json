{"metadata": {"version": "1.0.0", "description": "Cycle time analysis scenarios with flow optimization exercises", "lastUpdated": "2025-01-15"}, "scenarios": [{"id": 1, "title": "Code Review Bottleneck", "description": "Development team with slow code review process impacting overall flow", "context": "A 5-person development team where code reviews are taking too long, creating a bottleneck in the development process.", "workflow": {"stages": ["Backlog", "Development", "Code Review", "Testing", "Deployment", "Done"], "stageData": [{"stage": "Backlog", "averageTime": 0.5, "description": "Time from backlog to development start", "capacity": "Unlimited", "bottleneck": false}, {"stage": "Development", "averageTime": 3.2, "description": "Active coding time", "capacity": "5 developers", "bottleneck": false}, {"stage": "Code Review", "averageTime": 4.8, "description": "Waiting for and completing code review", "capacity": "2 senior developers", "bottleneck": true}, {"stage": "Testing", "averageTime": 1.5, "description": "QA testing and bug fixes", "capacity": "2 QA engineers", "bottleneck": false}, {"stage": "Deployment", "averageTime": 0.8, "description": "Deployment and verification", "capacity": "DevOps automation", "bottleneck": false}]}, "totalCycleTime": 10.8, "workItems": [{"id": "US-101", "backlog": 0.3, "development": 2.8, "review": 5.2, "testing": 1.2, "deployment": 0.5}, {"id": "US-102", "backlog": 0.8, "development": 3.5, "review": 4.1, "testing": 1.8, "deployment": 1.2}, {"id": "US-103", "backlog": 0.2, "development": 3.8, "review": 6.2, "testing": 1.1, "deployment": 0.7}, {"id": "US-104", "backlog": 0.6, "development": 2.9, "review": 3.8, "testing": 2.1, "deployment": 0.9}, {"id": "US-105", "backlog": 0.4, "development": 3.1, "review": 5.8, "testing": 1.4, "deployment": 0.6}], "questions": [{"type": "bottleneck_identification", "question": "Which stage is the primary bottleneck in this workflow?", "options": ["Development", "Code Review", "Testing", "Deployment"], "correct": 1, "explanation": "Code Review has the highest average time (4.8 days) and limited capacity (2 reviewers), making it the bottleneck"}, {"type": "improvement_strategy", "question": "What would be the most effective improvement?", "options": ["Hire more developers", "Add more QA engineers", "Train more people to do code reviews", "Automate deployment"], "correct": 2, "explanation": "Training more people to do code reviews would increase capacity at the bottleneck stage"}, {"type": "impact_analysis", "question": "If code review time was reduced to 2 days, what would be the new cycle time?", "options": ["6.0 days", "8.0 days", "7.2 days", "9.5 days"], "correct": 1, "explanation": "Reducing code review from 4.8 to 2.0 days would reduce total cycle time by 2.8 days: 10.8 - 2.8 = 8.0 days"}], "improvements": [{"strategy": "Increase Review Capacity", "actions": ["Train junior developers in code review", "Pair programming to reduce review needs", "Automated code quality checks"], "expectedImpact": "Reduce review time by 40-60%"}, {"strategy": "Reduce Review Scope", "actions": ["Smaller pull requests", "Pre-review automated checks", "Focus reviews on critical areas"], "expectedImpact": "Reduce review time by 30-40%"}]}, {"id": 2, "title": "Testing Constraint", "description": "Team with limited QA capacity creating testing bottleneck", "context": "A development team with 6 developers but only 1 QA engineer, creating a testing bottleneck.", "workflow": {"stages": ["Backlog", "Development", "Code Review", "Testing", "Deployment", "Done"], "stageData": [{"stage": "Backlog", "averageTime": 0.3, "description": "Time from backlog to development start", "capacity": "Unlimited", "bottleneck": false}, {"stage": "Development", "averageTime": 2.8, "description": "Active coding time", "capacity": "6 developers", "bottleneck": false}, {"stage": "Code Review", "averageTime": 1.5, "description": "Code review process", "capacity": "4 senior developers", "bottleneck": false}, {"stage": "Testing", "averageTime": 6.2, "description": "QA testing and bug fixes", "capacity": "1 QA engineer", "bottleneck": true}, {"stage": "Deployment", "averageTime": 0.5, "description": "Deployment and verification", "capacity": "Automated", "bottleneck": false}]}, "totalCycleTime": 11.3, "workItems": [{"id": "US-201", "backlog": 0.2, "development": 2.5, "review": 1.2, "testing": 7.1, "deployment": 0.4}, {"id": "US-202", "backlog": 0.4, "development": 3.1, "review": 1.8, "testing": 5.8, "deployment": 0.6}, {"id": "US-203", "backlog": 0.3, "development": 2.9, "review": 1.4, "testing": 6.8, "deployment": 0.5}, {"id": "US-204", "backlog": 0.5, "development": 2.6, "review": 1.6, "testing": 5.2, "deployment": 0.7}, {"id": "US-205", "backlog": 0.1, "development": 3.2, "review": 1.3, "testing": 6.9, "deployment": 0.3}], "questions": [{"type": "capacity_analysis", "question": "What's the root cause of the long cycle time?", "options": ["Slow development", "Complex code reviews", "Testing bottleneck", "Deployment issues"], "correct": 2, "explanation": "Testing takes 6.2 days on average with only 1 QA engineer for 6 developers - a clear capacity mismatch"}, {"type": "solution_prioritization", "question": "What's the highest impact solution?", "options": ["Hire another QA engineer", "Implement automated testing", "Reduce development team size", "Skip some testing"], "correct": 1, "explanation": "Automated testing would reduce the QA bottleneck without adding headcount and provide faster feedback"}], "improvements": [{"strategy": "Test Automation", "actions": ["Unit test coverage", "Integration test automation", "UI test automation"], "expectedImpact": "Reduce testing time by 50-70%"}, {"strategy": "Shift Left Testing", "actions": ["Developer testing", "Test-driven development", "Early QA involvement"], "expectedImpact": "Reduce testing time by 30-50%"}]}], "flowPrinciples": {"theory_of_constraints": {"description": "System throughput is limited by its slowest component", "application": "Identify and optimize the bottleneck stage", "key_insight": "Improving non-bottleneck stages won't improve overall flow"}, "little_law": {"description": "Cycle Time = Work in Progress / Throughput", "application": "Reduce WIP to reduce cycle time", "key_insight": "More work in progress leads to longer cycle times"}, "flow_efficiency": {"description": "Ratio of active work time to total cycle time", "application": "Minimize waiting time between stages", "key_insight": "Most cycle time is waiting, not working"}}, "improvementStrategies": {"increase_capacity": {"description": "Add more resources to bottleneck stage", "pros": ["Direct impact", "Clear solution"], "cons": ["Expensive", "May create new bottlenecks"], "when_to_use": "When bottleneck is clearly identified and sustainable"}, "reduce_demand": {"description": "Decrease work flowing into bottleneck", "pros": ["Cost effective", "Improves quality"], "cons": ["May reduce overall throughput"], "when_to_use": "When bottleneck can't be easily expanded"}, "eliminate_waste": {"description": "Remove non-value-adding activities", "pros": ["No additional cost", "Improves efficiency"], "cons": ["Requires process change"], "when_to_use": "When waste is identified in the process"}, "automate": {"description": "Use technology to increase capacity", "pros": ["Scalable", "Consistent quality"], "cons": ["Upfront investment", "Maintenance overhead"], "when_to_use": "For repetitive, rule-based activities"}}, "measurementTips": {"data_collection": ["Track work items through all stages", "Measure both active and waiting time", "Include weekends and holidays in calculations", "Distinguish between different work types"], "analysis_techniques": ["Use cumulative flow diagrams", "Calculate flow efficiency ratios", "Identify patterns in cycle time variation", "Segment analysis by work type or team"], "common_mistakes": ["Only measuring development time", "Ignoring waiting time between stages", "Not accounting for rework cycles", "Focusing on individual productivity vs. flow"]}}